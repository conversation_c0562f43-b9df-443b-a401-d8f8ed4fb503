// Content Configuration
import { ContentConfig } from '../types';
import { aboutPageContent, contactPageContent, privacyPageContent, termsPageContent } from './pages';
import { footerConfig } from './footer';

export const content: ContentConfig = {
  hero: {
    searchPlaceholder: 'Find coupons for brands, stores, and deals you want'
  },
  sections: {
    categories: 'Browse Categories',
    brands: 'Top Brands',
    featuredCoupons: 'Exclusive Coupons',
    featuredDeals: 'Limited Time Deals'
  },
  buttons: {
    search: 'Find Deals',
    viewAll: 'View All Deals',
    getCoupon: 'Get Code',
    getDeal: 'Get Deal',
    subscribe: 'Subscribe',
    unsubscribe: 'Unsubscribe'
  },
  messages: {
    loading: 'Fetching your coupons, please wait!',
    noResults: 'Oops! No coupons match your search criteria.',
    error: 'Sorry, something went wrong. Please try again.',
    success: 'Yay! Your subscription was successful!'
  },
  pageContent: {
    brands: {
      title: 'Explore Top Brands',
      subtitle: 'Discover exclusive offers from your favorite brands and retailers.',
      filterTitle: 'Discover Your Favorite Brands',
      noResultsTitle: 'Oops! No Brands Found',
      noResultsMessage: 'Try a different keyword or check back later!'
    },
    coupons: {
      title: 'Exclusive Savings Hub',
      subtitle: 'Get handpicked, verified promo codes for maximum savings.',
      filterTitle: 'Your Personalized Deals Await',
      noResultsTitle: 'No Codes Found',
      noResultsMessage: 'Try adjusting filters or check back soon!'
    },
    deals: {
      title: 'Hot Deals Alert!',
      subtitle: 'Unlock exclusive, limited-time offers from top brands!',
      filterTitle: 'Discover Your Deals',
      noResultsTitle: 'No Deals Found',
      noResultsMessage: 'Sorry, no deals match your search.'
    }
  },
  newsletter: {
    title: 'Unlock VIP Savings Early! 🚨',
    description: 'Be first to grab premium coupons & secret sales!',
    placeholder: 'Unlock savings with your email',
    benefits: ['Exclusive deals', 'VIP access', 'Flash sales'],
    privacy: 'We value privacy - your data stays protected'
  },
  footer: footerConfig,
  staticPages: {
    about: aboutPageContent,
    contact: contactPageContent,
    privacy: privacyPageContent,
    terms: termsPageContent
  }
};
