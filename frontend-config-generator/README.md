# Frontend Config Generator

Simple AI-powered frontend configuration generator for coupon websites.

## Usage

```bash
# Run the generator
python3 main.py
```

## Configuration

Edit `config.yaml` to customize your site settings:

```yaml
site:
  name: "YourSiteName"
  domain: "your-domain.com"
  api_url: "http://api.your-domain.com"
  contact_email: "<EMAIL>"
```

## Generated Files

The generator creates four configuration files in the `output/` directory:

- `variables.ts` - Global site variables
- `branding.ts` - Brand colors and visual identity  
- `content.ts` - Website content and copy
- `theme.ts` - Complete theme configuration

Files are automatically deployed to `../coupon-frontend/src/config/` if the directory exists.

## Requirements

- Python 3.8+
- PyYAML

Install dependencies:
```bash
pip install pyyaml
```

That's it! Simple and clean.
