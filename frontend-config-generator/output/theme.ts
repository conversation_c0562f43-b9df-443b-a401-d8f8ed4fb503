import { ThemeConfig } from './types';

export const defaultTheme: ThemeConfig = {
  name: 'CouponsGo Default',
  description: 'Default theme for CouponsGo website',
  version: '1.0.0',

  colors: {
    primary: {
      50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
      500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d', 950: '#052e16'
    },
    secondary: {
      50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa',
      500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
    },
    neutral: {
      50: '#fafafa', 100: '#f5f5f5', 200: '#e5e5e5', 300: '#d4d4d4', 400: '#a3a3a3',
      500: '#737373', 600: '#525252', 700: '#404040', 800: '#262626', 900: '#171717', 950: '#0a0a0a'
    }
  },

  components: {
    brandCard: {
      container: { background: "#ffffff", border: "#e5e7eb", shadow: "0 2px 8px rgba(0, 0, 0, 0.1)" }
    }
  },

  gradients: {
    hero: 'linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)',
    button: 'linear-gradient(to right, #16a34a, #15803d)'
  },

  borderRadius: { sm: '0.25rem', md: '0.375rem', lg: '0.5rem', xl: '0.75rem', full: '9999px' },
  spacing: { xs: '0.5rem', sm: '0.75rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
  fontSize: { xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem' }
};
