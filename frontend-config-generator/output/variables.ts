// Global variables configuration
export const SITE_VARIABLES = {
  SITE_NAME: 'CouponsGo',
  SITE_DOMAIN: 'coupons-go.org',
  SITE_URL: 'https://coupons-go.org',
  API_DOMAIN: 'api.coupons-go.org',
  CONTACT_EMAIL: '<EMAIL>',
  
  APP_VERSION: '1.0.0',
  CURRENT_YEAR: new Date().getFullYear().toString(),
  CURRENT_DATE: new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }),
  
  TAGLINE: 'Unlock Joyful Savings Today',
  SHORT_DESCRIPTION: 'CouponsGo: Your go-to for massive discounts and deals on everything you need!',
  
  FEATURES: {
    VERIFIED_COUPONS: 'Verified Savings',
    EXCLUSIVE_DEALS: 'Exclusive Deals',
    PRICE_ALERTS: 'Price Alerts',
    CASHBACK: 'Cashback Rewards'
  },

  ENVIRONMENT: {
    API_BASE_URL_PRODUCTION: 'http://api.coupons-go.org',
    API_BASE_URL_DEVELOPMENT: 'http://localhost:8080',
    IS_PRODUCTION: true
  }
} as const;

export const replaceVariables = (template: string): string => {
  let result = template;
  Object.entries(SITE_VARIABLES).forEach(([key, value]) => {
    if (typeof value === 'string') {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });
  return result;
};

export const COMBINED_VARIABLES = {
  FULL_SITE_TITLE: `${SITE_VARIABLES.SITE_NAME} - ${SITE_VARIABLES.TAGLINE}`,
  CONTACT_INFO: SITE_VARIABLES.CONTACT_EMAIL,
  COPYRIGHT: `© ${SITE_VARIABLES.CURRENT_YEAR} ${SITE_VARIABLES.SITE_NAME}. All rights reserved.`,
} as const;
