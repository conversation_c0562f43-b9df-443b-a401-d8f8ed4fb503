#!/usr/bin/env python3
"""
Frontend Config Generator - Main Entry Point
Usage: python3 main.py
"""

import os
import sys
import yaml
import shutil
from pathlib import Path

def load_config():
    """Load configuration from config.yaml"""
    config_path = Path("config.yaml")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    else:
        return {
            "site": {
                "name": "CouponsGo",
                "domain": "coupons-go.org", 
                "api_url": "http://api.coupons-go.org",
                "contact_email": "<EMAIL>"
            },
            "ai": {"model": "gemini_2_5_pro", "proxy": ""},
            "output": {"directory": "./output"},
            "generated_files": ["variables.ts", "branding.ts", "content.ts", "theme.ts"]
        }

def generate_variables(site):
    """Generate variables.ts content"""
    return f"""// Global variables configuration
export const SITE_VARIABLES = {{
  SITE_NAME: '{site["name"]}',
  SITE_DOMAIN: '{site["domain"]}',
  SITE_URL: 'https://{site["domain"]}',
  API_DOMAIN: '{site["api_url"].replace("http://", "").replace("https://", "")}',
  CONTACT_EMAIL: '{site.get("contact_email", f"support@{site['domain']}")}',
  
  APP_VERSION: '1.0.0',
  CURRENT_YEAR: new Date().getFullYear().toString(),
  CURRENT_DATE: new Date().toLocaleDateString('en-US', {{
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }}),
  
  TAGLINE: 'Unlock Joyful Savings Today',
  SHORT_DESCRIPTION: '{site["name"]}: Your go-to for massive discounts and deals on everything you need!',
  
  FEATURES: {{
    VERIFIED_COUPONS: 'Verified Savings',
    EXCLUSIVE_DEALS: 'Exclusive Deals',
    PRICE_ALERTS: 'Price Alerts',
    CASHBACK: 'Cashback Rewards'
  }},

  ENVIRONMENT: {{
    API_BASE_URL_PRODUCTION: '{site["api_url"]}',
    API_BASE_URL_DEVELOPMENT: 'http://localhost:8080',
    IS_PRODUCTION: true
  }}
}} as const;

export const replaceVariables = (template: string): string => {{
  let result = template;
  Object.entries(SITE_VARIABLES).forEach(([key, value]) => {{
    if (typeof value === 'string') {{
      const regex = new RegExp(`{{{{${{key}}}}}}`, 'g');
      result = result.replace(regex, value);
    }}
  }});
  return result;
}};

export const COMBINED_VARIABLES = {{
  FULL_SITE_TITLE: `${{SITE_VARIABLES.SITE_NAME}} - ${{SITE_VARIABLES.TAGLINE}}`,
  CONTACT_INFO: SITE_VARIABLES.CONTACT_EMAIL,
  COPYRIGHT: `© ${{SITE_VARIABLES.CURRENT_YEAR}} ${{SITE_VARIABLES.SITE_NAME}}. All rights reserved.`,
}} as const;
"""

def main():
    print("🎯 Frontend Config Generator")
    print("=" * 40)
    
    config = load_config()
    site = config["site"]
    
    # Create output directory
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    print("🚀 Generating configuration files...")
    
    # Generate files
    files = {
        "variables.ts": generate_variables(site),
        "branding.ts": f"""import {{ BrandingConfig }} from '../types';
import {{ SITE_VARIABLES }} from './variables';

export const branding: BrandingConfig = {{
  logo: {{
    text: SITE_VARIABLES.SITE_NAME,
    icon: '💎',
    iconBg: 'bg-gradient-to-bl from-green-400 to-blue-500'
  }},
  colors: {{
    primary: '#4a90e2',
    secondary: '#50b3a2',
    accent: '#9b59b6'
  }},
  fonts: {{
    primary: 'Inter',
    secondary: 'Inter'
  }}
}};
""",
        "content.ts": """// Content Configuration
import { ContentConfig } from '../types';
import { aboutPageContent, contactPageContent, privacyPageContent, termsPageContent } from './pages';
import { footerConfig } from './footer';

export const content: ContentConfig = {
  hero: {
    searchPlaceholder: 'Find coupons for brands, stores, and deals you want'
  },
  sections: {
    categories: 'Browse Categories',
    brands: 'Top Brands',
    featuredCoupons: 'Exclusive Coupons',
    featuredDeals: 'Limited Time Deals'
  },
  buttons: {
    search: 'Find Deals',
    viewAll: 'View All Deals',
    getCoupon: 'Get Code',
    getDeal: 'Get Deal',
    subscribe: 'Subscribe',
    unsubscribe: 'Unsubscribe'
  },
  messages: {
    loading: 'Fetching your coupons, please wait!',
    noResults: 'Oops! No coupons match your search criteria.',
    error: 'Sorry, something went wrong. Please try again.',
    success: 'Yay! Your subscription was successful!'
  },
  pageContent: {
    brands: {
      title: 'Explore Top Brands',
      subtitle: 'Discover exclusive offers from your favorite brands and retailers.',
      filterTitle: 'Discover Your Favorite Brands',
      noResultsTitle: 'Oops! No Brands Found',
      noResultsMessage: 'Try a different keyword or check back later!'
    },
    coupons: {
      title: 'Exclusive Savings Hub',
      subtitle: 'Get handpicked, verified promo codes for maximum savings.',
      filterTitle: 'Your Personalized Deals Await',
      noResultsTitle: 'No Codes Found',
      noResultsMessage: 'Try adjusting filters or check back soon!'
    },
    deals: {
      title: 'Hot Deals Alert!',
      subtitle: 'Unlock exclusive, limited-time offers from top brands!',
      filterTitle: 'Discover Your Deals',
      noResultsTitle: 'No Deals Found',
      noResultsMessage: 'Sorry, no deals match your search.'
    }
  },
  newsletter: {
    title: 'Unlock VIP Savings Early! 🚨',
    description: 'Be first to grab premium coupons & secret sales!',
    placeholder: 'Unlock savings with your email',
    benefits: ['Exclusive deals', 'VIP access', 'Flash sales'],
    privacy: 'We value privacy - your data stays protected'
  },
  footer: footerConfig,
  staticPages: {
    about: aboutPageContent,
    contact: contactPageContent,
    privacy: privacyPageContent,
    terms: termsPageContent
  }
};
""",
        "theme.ts": """import { ThemeConfig } from './types';

export const defaultTheme: ThemeConfig = {
  name: 'CouponsGo Default',
  description: 'Default theme for CouponsGo website',
  version: '1.0.0',

  colors: {
    primary: {
      50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
      500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d', 950: '#052e16'
    },
    secondary: {
      50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa',
      500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
    },
    neutral: {
      50: '#fafafa', 100: '#f5f5f5', 200: '#e5e5e5', 300: '#d4d4d4', 400: '#a3a3a3',
      500: '#737373', 600: '#525252', 700: '#404040', 800: '#262626', 900: '#171717', 950: '#0a0a0a'
    }
  },

  components: {
    brandCard: {
      container: { background: "#ffffff", border: "#e5e7eb", shadow: "0 2px 8px rgba(0, 0, 0, 0.1)" }
    }
  },

  gradients: {
    hero: 'linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)',
    button: 'linear-gradient(to right, #16a34a, #15803d)'
  },

  borderRadius: { sm: '0.25rem', md: '0.375rem', lg: '0.5rem', xl: '0.75rem', full: '9999px' },
  spacing: { xs: '0.5rem', sm: '0.75rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
  fontSize: { xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem' }
};
"""
    }
    
    # Write files
    for filename, content in files.items():
        file_path = output_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Generated: {filename}")
    
    # Deploy to frontend directory
    target_base = Path("../coupon-frontend/src/config")
    if target_base.exists():
        mappings = {
            "variables.ts": target_base / "site" / "variables.ts",
            "branding.ts": target_base / "site" / "branding.ts",
            "content.ts": target_base / "site" / "content.ts",
            "theme.ts": target_base / "theme" / "default.ts"
        }
        
        for src, dst in mappings.items():
            src_path = output_dir / src
            if src_path.exists():
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst)
                print(f"📁 Deployed: {src} -> {dst.relative_to(target_base.parent.parent)}")
    
    print("\n🎉 All configuration files generated and deployed successfully!")

if __name__ == "__main__":
    main()
